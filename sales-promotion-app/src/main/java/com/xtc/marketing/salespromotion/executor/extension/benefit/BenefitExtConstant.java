package com.xtc.marketing.salespromotion.executor.extension.benefit;

/**
 * 权益扩展点配置
 */
public class BenefitExtConstant {

    private BenefitExtConstant() {
    }

    /**
     * 用例
     */
    public static final String USE_CASE = "benefitExtPt";

    /**
     * 场景：优惠金额
     */
    public static final String SCENARIO_DISCOUNT_AMOUNT = "discountAmount";
    /**
     * 场景：赠送积分
     */
    public static final String SCENARIO_POINT = "point";

    /**
     * 场景：积分倍数
     */
    public static final String SCENARIO_POINT_MULTIPLE = "pointMultiple";

    /**
     * 场景：赠送商品
     */
    public static final String SCENARIO_GIFT = "gift";
    /**
     * 场景：优惠券
     */
    public static final String SCENARIO_COUPON = "coupon";

}
