package com.xtc.marketing.salespromotion.executor.query;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.salespromotion.bo.BenefitBO;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionShopDTO;
import com.xtc.marketing.salespromotion.dto.query.PromotionShopListQry;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.exception.BizException;
import com.xtc.marketing.salespromotion.executor.checker.ScopeChecker;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtConstant;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtPt;
import com.xtc.marketing.salespromotion.repository.SalesPromotionRepository;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.CollectionCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 门店促销查询执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SalesPromotionShopQryExe {

    private final SalesPromotionRepository salesPromotionRepository;
    private final PromotionLevelQryExe promotionLevelQryExe;
    private final ScopeChecker scopeChecker;
    private final ExtensionExecutor extensionExecutor;

    /**
     * 执行门店促销查询
     *
     * @param qry 查询参数
     * @return 门店促销列表
     */
    public List<SalesPromotionShopDTO> execute(PromotionShopListQry qry) {
        // 获取促销配置集合
        List<SalesPromotionDO> salesPromotions = this.getPromotions(qry);

        // 如果提供了商品或SKU信息，则进行促销范围过滤
        if (this.needScopeFilter(qry)) {
            // 过滤出符合范围的促销配置
            salesPromotions = salesPromotions.stream()
                    .filter(promotion -> this.filterScopeByParams(promotion, qry))
                    .collect(Collectors.toList());
        }

        // 类型转换，使用 CollectionCopier 的回调函数处理 promotionType.name 转换
        return CollectionCopier.copy(salesPromotions, SalesPromotionShopDTO::new, (s, t) -> {
            // 将 PromotionType 枚举转换为字符串
            if (s.getPromotionType() != null) {
                t.setPromotionType(s.getPromotionType().name());
            }
        });
    }

    /**
     * 根据参数确认是否符合促销范围
     *
     * @param promotion 促销配置
     * @param qry       查询参数
     */
    private boolean filterScopeByParams(SalesPromotionDO promotion, PromotionShopListQry qry) {
        try {
            scopeChecker.check(promotion.getPromotionScope(),
                    qry.getAgentCode(),
                    qry.getSecondAgentCode(),
                    qry.getShopId(),
                    qry.getProductId(),
                    qry.getSkuId());
            return true;
        } catch (BizException e) {
            log.debug("不符合促销范围 promotionToken: {}, qry: {}", promotion.getPromotionToken(), qry);
            return false;
        } catch (Exception e) {
            log.error("判断促销范围异常 promotionToken: {}", promotion.getPromotionToken(), e);
            return false;
        }
    }

    /**
     * 判断是否需要进行促销范围过滤
     *
     * @param qry 查询参数
     * @return 是否需要范围过滤
     */
    private boolean needScopeFilter(PromotionShopListQry qry) {
        return qry.getProductId() != null || qry.getSkuId() != null;
    }

    /**
     * 获取门店相关的促销配置集合
     *
     * @param qry 查询参数
     * @return 促销配置集合
     */
    private List<SalesPromotionDO> getPromotions(PromotionShopListQry qry) {
        Set<String> promotionLevel = promotionLevelQryExe.execute(qry.getAgentCode(), qry.getSecondAgentCode(), qry.getShopId());
        return salesPromotionRepository.listEnabledByLevel(qry.getBizCode(), LocalDateTime.now(), promotionLevel);
    }

}
