package com.xtc.marketing.salespromotion.executor.query;

import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionShopDTO;
import com.xtc.marketing.salespromotion.dto.query.PromotionShopListQry;
import com.xtc.marketing.salespromotion.repository.SalesPromotionRepository;
import com.xtc.marketing.salespromotion.util.CollectionCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 门店促销查询执行器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SalesPromotionShopQryExe {

    private final SalesPromotionRepository salesPromotionRepository;
    private final PromotionLevelQryExe promotionLevelQryExe;

    /**
     * 执行门店促销查询
     *
     * @param qry 查询参数
     * @return 门店促销列表
     */
    public List<SalesPromotionShopDTO> execute(PromotionShopListQry qry) {
        // 获取促销配置集合
        List<SalesPromotionDO> salesPromotions = this.getPromotions(qry);

        // 类型转换，使用 CollectionCopier 的回调函数处理 promotionType.name 转换
        return CollectionCopier.copy(salesPromotions, SalesPromotionShopDTO::new, (s, t) -> {
            // 将 PromotionType 枚举转换为字符串
            if (s.getPromotionType() != null) {
                t.setPromotionType(s.getPromotionType().name());
            }
        });
    }

    /**
     * 获取门店相关的促销配置集合
     *
     * @param qry 查询参数
     * @return 促销配置集合
     */
    private List<SalesPromotionDO> getPromotions(PromotionShopListQry qry) {
        Set<String> promotionLevel = promotionLevelQryExe.execute(qry.getAgentCode(), qry.getSecondAgentCode(), qry.getShopId());
        return salesPromotionRepository.listEnabledByLevel(qry.getBizCode(), LocalDateTime.now(), promotionLevel);
    }

}
