package com.xtc.marketing.salespromotion.executor.extension.rule;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = RuleExtConstant.USE_CASE, scenario = RuleExtConstant.SCENARIO_MEMBER_DAY)
public class MemberDayRuleExt implements RuleExtPt {

    @Override
    public boolean checkSingle(String paramConfig, List<SkuQry> skus) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        LocalDate now = LocalDate.now();

        boolean allow;
        switch (param.getDayType()) {
            case EVERY_MONTH:
                allow = now.getDayOfMonth() == param.getDay();
                break;
            case EVERY_WEEK:
                allow = now.getDayOfWeek().getValue() == param.getDay();
                break;
            default:
                allow = false;
        }
        return allow;
    }

    @Override
    public boolean checkCompose(String paramConfig, List<SkuQry> skus) {
        return this.checkSingle(paramConfig, skus);
    }

    /**
     * 会员日参数
     */
    @Data
    class Param {

        /**
         * 会员日类型
         */
        private DayType dayType;

        /**
         * 会员日（MONTH：1-28，WEEK：1-7）
         */
        private Integer day;

    }

    /**
     * 会员日类型
     */
    enum DayType {

        /**
         * 每月
         */
        EVERY_MONTH,
        /**
         * 每周
         */
        EVERY_WEEK,

    }

}
