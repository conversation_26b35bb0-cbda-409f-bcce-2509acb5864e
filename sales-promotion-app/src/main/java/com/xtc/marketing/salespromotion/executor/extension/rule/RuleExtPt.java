package com.xtc.marketing.salespromotion.executor.extension.rule;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;

import java.util.List;

/**
 * 规则扩展点
 */
public interface RuleExtPt extends ExtensionPointI {

    /**
     * 检查符合规则（单品优惠）
     *
     * @param paramConfig 规则参数配置
     * @param skus        sku集合
     * @return 检查结果
     */
    boolean checkSingle(String paramConfig, List<SkuQry> skus);

    /**
     * 检查符合规则（组合优惠）
     *
     * @param paramConfig 规则参数配置
     * @param skus        sku集合
     * @return 检查结果
     */
    boolean checkCompose(String paramConfig, List<SkuQry> skus);

}
