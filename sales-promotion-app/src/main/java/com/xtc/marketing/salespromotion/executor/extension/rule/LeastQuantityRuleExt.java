package com.xtc.marketing.salespromotion.executor.extension.rule;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = RuleExtConstant.USE_CASE, scenario = RuleExtConstant.SCENARIO_LEAST_QUANTITY)
public class LeastQuantityRuleExt implements RuleExtPt {

    @Override
    public boolean checkSingle(String paramConfig, List<SkuQry> skus) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        // 判断每个sku是否满足规则，删除不满足规则的sku
        skus.removeIf(sku -> sku.getSkuNum() < param.getLeastQuantity());
        return !skus.isEmpty();
    }

    @Override
    public boolean checkCompose(String paramConfig, List<SkuQry> skus) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        int skuTotalQuantity = skus.stream().mapToInt(SkuQry::getSkuNum).sum();
        return skuTotalQuantity >= param.getLeastQuantity();
    }

    /**
     * 满X件参数
     */
    @Data
    class Param {

        /**
         * 满X件
         */
        private Integer leastQuantity;

    }

}
