package com.xtc.marketing.salespromotion.executor.checker;

import com.xtc.marketing.salespromotion.bo.PromotionScopeBO;
import com.xtc.marketing.salespromotion.bo.ScopeBO;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.enums.ScopeType;
import com.xtc.marketing.salespromotion.exception.BizException;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class ScopeChecker {

    /**
     * 促销范围检查
     *
     * @param promotionScopeConfig 促销范围配置
     * @param sku                  sku
     */
    public void check(String promotionScopeConfig, SkuQry sku) {
        PromotionScopeBO promotionScope = GsonUtil.jsonToBean(promotionScopeConfig, PromotionScopeBO.class);

        // 检查符合代理范围
        this.checkScope(promotionScope.getAgentScope(), sku.getAgentCode());

        // 检查符合二级代理范围
        this.checkScope(promotionScope.getSecondAgentScope(), sku.getSecondAgentCode());

        // 检查符合门店范围
        this.checkScope(promotionScope.getShopScope(), sku.getShopId());

        // 检查符合商品范围
        this.checkScope(promotionScope.getProductScope(), sku.getProductId());

        // 检查符合sku范围
        this.checkScope(promotionScope.getSkuScope(), sku.getSkuId());
    }

    /**
     * 促销范围检查（代理、二代、门店）
     *
     * @param promotionScopeConfig 促销范围配置
     * @param agentCode            代理代码
     * @param secondAgentCode      二级代理代码
     * @param shopId               门店id
     * @param productId            商品id（可选）
     * @param skuId                skuid（可选）
     */
    public void check(String promotionScopeConfig, String agentCode, String secondAgentCode, String shopId, String productId, String skuId) {
        PromotionScopeBO promotionScope = GsonUtil.jsonToBean(promotionScopeConfig, PromotionScopeBO.class);

        // 检查符合代理范围
        this.checkScope(promotionScope.getAgentScope(), agentCode);

        // 检查符合二级代理范围
        this.checkScope(promotionScope.getSecondAgentScope(), secondAgentCode);

        // 检查符合门店范围
        this.checkScope(promotionScope.getShopScope(), shopId);

        // 检查符合商品范围（如果提供了商品id）
        if (StringUtils.isNotBlank(productId)) {
            this.checkScope(promotionScope.getProductScope(), productId);
        }

        // 检查符合sku范围（如果提供了skuid）
        if (StringUtils.isNotBlank(skuId)) {
            this.checkScope(promotionScope.getSkuScope(), skuId);
        }
    }

    /**
     * 检查符合范围
     *
     * @param scopeBO 范围
     * @param id      id
     */
    private void checkScope(ScopeBO scopeBO, String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        if (scopeBO.getScopeType() == ScopeType.GLOBAL) {
            return;
        }

        boolean contains = scopeBO.getIds().contains(id);
        if (scopeBO.getScopeType() == ScopeType.INCLUDE) {
            if (contains) {
                return;
            }
            throw BizException.of("不在促销范围内");
        }
        if (scopeBO.getScopeType() == ScopeType.EXCLUDE && contains) {
            throw BizException.of("不在促销范围内");
        }
    }

}
