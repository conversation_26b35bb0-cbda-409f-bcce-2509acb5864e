package com.xtc.marketing.salespromotion.executor.checker;

import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 促销范围检查器测试
 */
@ExtendWith(MockitoExtension.class)
class ScopeCheckerTest {

    @InjectMocks
    private ScopeChecker scopeChecker;

    private String globalScopeConfig;
    private String includeScopeConfig;
    private String excludeScopeConfig;

    @BeforeEach
    void setUp() {
        // 全局范围配置
        globalScopeConfig = """
                {
                    "agentScope": {"scopeType": "GLOBAL", "ids": []},
                    "secondAgentScope": {"scopeType": "GLOBAL", "ids": []},
                    "shopScope": {"scopeType": "GLOBAL", "ids": []},
                    "productScope": {"scopeType": "GLOBAL", "ids": []},
                    "skuScope": {"scopeType": "GLOBAL", "ids": []}
                }
                """;

        // 包含范围配置
        includeScopeConfig = """
                {
                    "agentScope": {"scopeType": "INCLUDE", "ids": ["AGENT001", "AGENT002"]},
                    "secondAgentScope": {"scopeType": "INCLUDE", "ids": ["SECOND001"]},
                    "shopScope": {"scopeType": "INCLUDE", "ids": ["SHOP001", "SHOP002"]},
                    "productScope": {"scopeType": "INCLUDE", "ids": ["PRODUCT001"]},
                    "skuScope": {"scopeType": "INCLUDE", "ids": ["SKU001", "SKU002"]}
                }
                """;

        // 排除范围配置
        excludeScopeConfig = """
                {
                    "agentScope": {"scopeType": "EXCLUDE", "ids": ["AGENT999"]},
                    "secondAgentScope": {"scopeType": "EXCLUDE", "ids": ["SECOND999"]},
                    "shopScope": {"scopeType": "EXCLUDE", "ids": ["SHOP999"]},
                    "productScope": {"scopeType": "EXCLUDE", "ids": ["PRODUCT999"]},
                    "skuScope": {"scopeType": "EXCLUDE", "ids": ["SKU999"]}
                }
                """;
    }

    @Test
    void testCheckWithSkuQry_GlobalScope() {
        // 构建SkuQry
        SkuQry sku = new SkuQry();
        sku.setAgentCode("AGENT001");
        sku.setSecondAgentCode("SECOND001");
        sku.setShopId("SHOP001");
        sku.setProductId("PRODUCT001");
        sku.setSkuId("SKU001");

        // 全局范围应该通过检查
        assertDoesNotThrow(() -> scopeChecker.check(globalScopeConfig, sku));
    }

    @Test
    void testCheckWithSkuQry_IncludeScope_Pass() {
        // 构建符合包含范围的SkuQry
        SkuQry sku = new SkuQry();
        sku.setAgentCode("AGENT001");
        sku.setSecondAgentCode("SECOND001");
        sku.setShopId("SHOP001");
        sku.setProductId("PRODUCT001");
        sku.setSkuId("SKU001");

        // 应该通过检查
        assertDoesNotThrow(() -> scopeChecker.check(includeScopeConfig, sku));
    }

    @Test
    void testCheckWithSkuQry_IncludeScope_Fail() {
        // 构建不符合包含范围的SkuQry
        SkuQry sku = new SkuQry();
        sku.setAgentCode("AGENT999"); // 不在包含列表中
        sku.setSecondAgentCode("SECOND001");
        sku.setShopId("SHOP001");
        sku.setProductId("PRODUCT001");
        sku.setSkuId("SKU001");

        // 应该抛出异常
        assertThrows(BizException.class, () -> scopeChecker.check(includeScopeConfig, sku));
    }

    @Test
    void testCheckWithParams_GlobalScope() {
        // 全局范围应该通过检查
        assertDoesNotThrow(() -> scopeChecker.check(globalScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "PRODUCT001", "SKU001"));
    }

    @Test
    void testCheckWithParams_IncludeScope_Pass() {
        // 符合包含范围应该通过检查
        assertDoesNotThrow(() -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "PRODUCT001", "SKU001"));
    }

    @Test
    void testCheckWithParams_IncludeScope_Fail() {
        // 不符合包含范围应该抛出异常
        assertThrows(BizException.class, () -> scopeChecker.check(includeScopeConfig, 
                "AGENT999", "SECOND001", "SHOP001", "PRODUCT001", "SKU001"));
    }

    @Test
    void testCheckWithParams_ExcludeScope_Pass() {
        // 不在排除列表中应该通过检查
        assertDoesNotThrow(() -> scopeChecker.check(excludeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "PRODUCT001", "SKU001"));
    }

    @Test
    void testCheckWithParams_ExcludeScope_Fail() {
        // 在排除列表中应该抛出异常
        assertThrows(BizException.class, () -> scopeChecker.check(excludeScopeConfig, 
                "AGENT999", "SECOND001", "SHOP001", "PRODUCT001", "SKU001"));
    }

    @Test
    void testCheckWithParams_NullProductAndSku() {
        // 商品和SKU为null时应该跳过相应检查
        assertDoesNotThrow(() -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", null, null));
    }

    @Test
    void testCheckWithParams_EmptyProductAndSku() {
        // 商品和SKU为空字符串时应该跳过相应检查
        assertDoesNotThrow(() -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "", ""));
    }

    @Test
    void testCheckWithParams_BlankProductAndSku() {
        // 商品和SKU为空白字符串时应该跳过相应检查
        assertDoesNotThrow(() -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "   ", "   "));
    }

    @Test
    void testCheckWithParams_OnlyRequiredFields() {
        // 只传入必需的代理、二代、门店字段
        assertDoesNotThrow(() -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", null, null));
    }

    @Test
    void testCheckWithParams_PartialFields() {
        // 部分字段为null
        assertDoesNotThrow(() -> scopeChecker.check(globalScopeConfig, 
                "AGENT001", null, "SHOP001", "PRODUCT001", null));
    }

    @Test
    void testCheckWithParams_ProductNotInScope() {
        // 商品不在包含范围内
        assertThrows(BizException.class, () -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "PRODUCT999", "SKU001"));
    }

    @Test
    void testCheckWithParams_SkuNotInScope() {
        // SKU不在包含范围内
        assertThrows(BizException.class, () -> scopeChecker.check(includeScopeConfig, 
                "AGENT001", "SECOND001", "SHOP001", "PRODUCT001", "SKU999"));
    }

}
