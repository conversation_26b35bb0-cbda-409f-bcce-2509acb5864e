package com.xtc.marketing.salespromotion.dto;

import com.xtc.marketing.salespromotion.enums.PromotionType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SalesPromotionDTO {

    /**
     * 促销编号
     */
    private String promotionToken;

    /**
     * 促销类型
     */
    private PromotionType promotionType;

    /**
     * 促销名称
     */
    private String promotionName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 促销金额（单位：分）
     */
    private Integer discountAmount;

    /**
     * 积分数量
     */
    private Integer point;

    /**
     * 积分倍数
     */
    private Integer pointMultiple;

    /**
     * 优惠券集合
     */
    private List<BenefitItemDTO> couponList;

    /**
     * 赠品sku集合
     */
    private List<BenefitItemDTO> giftSkuList;

    /**
     * 页面配置（业务定义）
     */
    private String webConfig;

    /**
     * 是否是积分权益促销
     */
    private Boolean pointBenefit;

}
