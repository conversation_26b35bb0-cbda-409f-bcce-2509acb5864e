package com.xtc.marketing.salespromotion.dto.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xtc.marketing.marketingcomponentdto.dto.BasePageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@ToString
public class SalesPromotionPageQry extends BasePageQuery {

    /**
     * 促销编号
     */
    @Length(max = 100)
    private String promotionToken;

    /**
     * 启用
     */
    private Boolean enabled;

    /**
     * 时间节点
     */
    private LocalDateTime time;

    /**
     * 业务代码
     */
    @Length(max = 50)
    private String bizCode;

    /**
     * 代理代码
     */
    @Length(max = 100)
    private String agentCode;

    /**
     * 二级代理代码
     */
    @Length(max = 100)
    private String secondAgentCode;

    /**
     * 门店id
     */
    @Length(max = 100)
    private String shopId;

    /**
     * 促销级别
     */
    @JsonIgnore
    @Size(min = 1, max = 20)
    private Set<String> promotionLevel;

}
