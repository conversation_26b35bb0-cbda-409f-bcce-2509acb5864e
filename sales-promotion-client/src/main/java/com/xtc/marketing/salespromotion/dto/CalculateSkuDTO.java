package com.xtc.marketing.salespromotion.dto;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * sku促销计价
 */
@Data
public class CalculateSkuDTO {

    /**
     * 门店id
     */
    private String shopId;

    /**
     * 代理代码
     */
    private String agentCode;

    /**
     * 二级代理代码
     */
    private String secondAgentCode;

    /**
     * 商品id
     */
    private String productId;

    /**
     * skuid
     */
    private String skuId;

    /**
     * sku数量
     */
    private Integer skuNum;

    /**
     * sku单价（单位：分）
     */
    private Integer skuUnitPrice;

    /**
     * 促销单价（单位：分）
     */
    private Integer salesPromotionUnitPrice;

    /**
     * 优惠后单价（单位：分） = sku单价 - 促销单价
     */
    private Integer unitPrice;

    /**
     * sku总金额（单位：分） = sku单价 * 数量
     */
    private Integer skuTotalPrice;

    /**
     * 促销总金额（单位：分） = 促销单价 * 数量
     */
    private Integer salesPromotionTotalPrice;

    /**
     * 优惠后总金额（单位：分） = sku总金额 - 促销总金额
     */
    private Integer totalPrice;

    /**
     * 可参与的促销活动集合
     */
    private List<PromotionDetailDTO> promotionDetails;

    public CalculateSkuDTO() {
        this.promotionDetails = new ArrayList<>();
    }

    /**
     * 初始化金额
     */
    public void initPrice() {
        this.salesPromotionUnitPrice = 0;
        this.unitPrice = this.skuUnitPrice;

        this.skuTotalPrice = this.skuUnitPrice * this.skuNum;
        this.salesPromotionTotalPrice = 0;
        this.totalPrice = this.skuTotalPrice;
    }

    public void addPromotionDetail(PromotionDetailDTO promotionDetail) {
        this.promotionDetails.add(promotionDetail);
    }

    public void addDiscountAmount(int discountAmount) {
        this.salesPromotionUnitPrice = this.salesPromotionUnitPrice + discountAmount;
        this.unitPrice = this.skuUnitPrice - this.salesPromotionUnitPrice;

        this.salesPromotionTotalPrice = this.salesPromotionUnitPrice * this.skuNum;
        this.totalPrice = this.skuTotalPrice - this.salesPromotionTotalPrice;
    }

    /**
     * 新增或者更新促销活动
     *
     * @param promotionDetail 促销活动
     */
    public void addOrUpdatePromotionDetail(PromotionDetailDTO promotionDetail) {
        Optional<PromotionDetailDTO> currentDetailOpt = this.promotionDetails.stream()
                .filter(detail -> detail.getPromotionToken().equals(promotionDetail.getPromotionToken()))
                .findFirst();
        if (currentDetailOpt.isPresent()) {
            // 汇总权益数据
            PromotionDetailDTO currentDetail = currentDetailOpt.get();
            if (promotionDetail.getDiscountAmount() != null) {
                int currentDiscountAmount = Optional.ofNullable(currentDetail.getDiscountAmount()).orElse(0);
                currentDetail.setDiscountAmount(currentDiscountAmount + promotionDetail.getDiscountAmount());
            } else if (promotionDetail.getPoint() != null) {
                int currentPoint = Optional.ofNullable(currentDetail.getPoint()).orElse(0);
                currentDetail.setPoint(currentPoint + promotionDetail.getPoint());
            } else if (promotionDetail.getPointMultiple() != null) {
                int currentPointMultiple = Optional.ofNullable(currentDetail.getPointMultiple()).orElse(0);
                currentDetail.setPointMultiple(currentPointMultiple + promotionDetail.getPointMultiple());
            } else if (!CollectionUtils.isEmpty(promotionDetail.getGiftSkuList())) {
                List<BenefitItemDTO> giftSkuList = Stream.of(currentDetail, promotionDetail)
                        .filter(o -> o.getGiftSkuList() != null)
                        .flatMap(o -> o.getGiftSkuList().stream())
                        .collect(Collectors.toList());
                currentDetail.setGiftSkuList(giftSkuList);
            } else if (!CollectionUtils.isEmpty(promotionDetail.getCouponList())) {
                List<BenefitItemDTO> couponList = Stream.of(currentDetail, promotionDetail)
                        .filter(o -> o.getCouponList() != null)
                        .flatMap(o -> o.getCouponList().stream())
                        .collect(Collectors.toList());
                currentDetail.setCouponList(couponList);
            }
        } else {
            this.promotionDetails.add(promotionDetail);
        }
    }

}
