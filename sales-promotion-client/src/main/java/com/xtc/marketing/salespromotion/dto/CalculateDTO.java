package com.xtc.marketing.salespromotion.dto;

import com.xtc.marketing.salespromotion.dto.query.CalculateQry;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 促销计价
 */
@Data
@NoArgsConstructor
public class CalculateDTO {

    /**
     * 原价
     */
    private Integer originalPrice;

    /**
     * 促销金额
     */
    private Integer salesPromotionPrice;

    /**
     * 优惠后金额 = 原价 - 促销金额
     */
    private Integer price;

    /**
     * sku促销集合
     */
    private List<CalculateSkuDTO> calculateSkus;

    /**
     * 可参与的促销活动集合
     */
    private List<PromotionDetailDTO> promotionDetails;

    public CalculateDTO(CalculateQry qry) {
        this.promotionDetails = new ArrayList<>();

        this.calculateSkus = qry.getSkus().stream()
                .map(sku -> {
                    CalculateSkuDTO calculateSkuDTO = new CalculateSkuDTO();
                    BeanUtils.copyProperties(sku, calculateSkuDTO);
                    calculateSkuDTO.initPrice();
                    return calculateSkuDTO;
                })
                .collect(Collectors.toList());

        this.originalPrice = this.calculateSkus.stream().mapToInt(CalculateSkuDTO::getTotalPrice).sum();
        this.salesPromotionPrice = 0;
        this.price = originalPrice - salesPromotionPrice;
    }

    public void addDiscountAmount(int discountAmount) {
        this.salesPromotionPrice = this.salesPromotionPrice + discountAmount;
        this.price = this.originalPrice - this.salesPromotionPrice;
    }

    /**
     * 新增或者更新促销活动
     *
     * @param promotionDetail 促销活动
     */
    public void addOrUpdatePromotionDetail(PromotionDetailDTO promotionDetail) {
        Optional<PromotionDetailDTO> currentDetailOpt = this.promotionDetails.stream()
                .filter(detail -> detail.getPromotionToken().equals(promotionDetail.getPromotionToken()))
                .findFirst();
        if (currentDetailOpt.isPresent()) {
            // 汇总权益数据
            PromotionDetailDTO currentDetail = currentDetailOpt.get();
            if (promotionDetail.getDiscountAmount() != null) {
                int currentDiscountAmount = Optional.ofNullable(currentDetail.getDiscountAmount()).orElse(0);
                currentDetail.setDiscountAmount(currentDiscountAmount + promotionDetail.getDiscountAmount());
            } else if (promotionDetail.getPoint() != null) {
                int currentPoint = Optional.ofNullable(currentDetail.getPoint()).orElse(0);
                currentDetail.setPoint(currentPoint + promotionDetail.getPoint());
            } else if (promotionDetail.getPointMultiple() != null) {
                int currentPointMultiple = Optional.ofNullable(currentDetail.getPointMultiple()).orElse(0);
                currentDetail.setPointMultiple(currentPointMultiple + promotionDetail.getPointMultiple());
            } else if (!CollectionUtils.isEmpty(promotionDetail.getGiftSkuList())) {
                List<BenefitItemDTO> giftSkuList = Stream.of(currentDetail, promotionDetail)
                        .filter(o -> o.getGiftSkuList() != null)
                        .flatMap(o -> o.getGiftSkuList().stream())
                        .collect(Collectors.toList());
                currentDetail.setGiftSkuList(giftSkuList);
            } else if (!CollectionUtils.isEmpty(promotionDetail.getCouponList())) {
                List<BenefitItemDTO> couponList = Stream.of(currentDetail, promotionDetail)
                        .filter(o -> o.getCouponList() != null)
                        .flatMap(o -> o.getCouponList().stream())
                        .collect(Collectors.toList());
                currentDetail.setCouponList(couponList);
            }
        } else {
            this.promotionDetails.add(promotionDetail);
        }
    }

}
