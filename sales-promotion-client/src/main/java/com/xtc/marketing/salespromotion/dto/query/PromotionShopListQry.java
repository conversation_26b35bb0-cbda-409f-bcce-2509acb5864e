package com.xtc.marketing.salespromotion.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 查询门店促销列表参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromotionShopListQry {

    /**
     * 业务代码
     */
    @NotBlank
    @Length(max = 50)
    private String bizCode;
    /**
     * 代理代码
     */
    @Length(max = 100)
    private String agentCode;
    /**
     * 二级代理代码
     */
    @Length(max = 100)
    private String secondAgentCode;
    /**
     * 门店id
     */
    @NotBlank
    @Length(max = 100)
    private String shopId;

}
