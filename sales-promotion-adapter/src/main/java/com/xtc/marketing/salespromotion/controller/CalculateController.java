package com.xtc.marketing.salespromotion.controller;

import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.marketing.salespromotion.SalesPromotionServiceI;
import com.xtc.marketing.salespromotion.annotation.RateLimiter;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionShopDTO;
import com.xtc.marketing.salespromotion.dto.query.CalculateQry;
import com.xtc.marketing.salespromotion.dto.query.PromotionListQry;
import com.xtc.marketing.salespromotion.dto.query.PromotionShopListQry;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 促销计价模块
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class CalculateController {

    private final SalesPromotionServiceI salesPromotionService;

    /**
     * 促销计价
     *
     * @param qry 参数
     * @return 促销计价结果
     */
    @RateLimiter
    @PostMapping("/sales-promotion/calculate")
    public SingleResponse<CalculateDTO> calculate(@Valid @RequestBody CalculateQry qry) {
        CalculateDTO calculateDTO = salesPromotionService.calculate(qry);
        return SingleResponse.of(calculateDTO);
    }

    /**
     * 查询促销列表
     *
     * @param qry 参数
     * @return 促销列表
     */
    @GetMapping("/sales-promotion/promotions")
    public MultiResponse<SalesPromotionDTO> listPromotions(@Valid PromotionListQry qry) {
        List<SalesPromotionDTO> salesPromotions = salesPromotionService.listSalesPromotions(qry);
        return MultiResponse.of(salesPromotions);
    }

    /**
     * 查询门店促销列表
     *
     * @param qry 参数
     * @return 门店促销列表
     */
    @GetMapping("/sales-promotion/shop-promotions")
    public MultiResponse<SalesPromotionShopDTO> listShopPromotions(@Valid PromotionShopListQry qry) {
        List<SalesPromotionShopDTO> shopPromotions = salesPromotionService.listShopPromotions(qry);
        return MultiResponse.of(shopPromotions);
    }

}
