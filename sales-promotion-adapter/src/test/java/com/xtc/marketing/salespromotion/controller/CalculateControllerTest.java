package com.xtc.marketing.salespromotion.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xtc.marketing.salespromotion.SalesPromotionServiceI;
import com.xtc.marketing.salespromotion.dto.SalesPromotionShopDTO;
import com.xtc.marketing.salespromotion.dto.query.PromotionShopListQry;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 计价控制器测试
 */
@WebMvcTest(CalculateController.class)
class CalculateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SalesPromotionServiceI salesPromotionService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testListShopPromotions() throws Exception {
        // 构建模拟返回数据
        SalesPromotionShopDTO shop1 = new SalesPromotionShopDTO();
        shop1.setBizCode("TEST_BIZ");
        shop1.setAgentCode("AGENT001");
        shop1.setPromotionToken("PROMO001");
        shop1.setPromotionType("SINGLE");
        shop1.setPromotionName("单品优惠");
        shop1.setPromotionDesc("测试单品优惠");
        shop1.setEnabled(true);
        shop1.setStartTime(LocalDateTime.now().minusDays(1));
        shop1.setEndTime(LocalDateTime.now().plusDays(1));
        shop1.setRuleDesc("满100元减10元");
        shop1.setPromotionLevel("SHOP001");
        shop1.setPromotionScope("ALL");
        shop1.setRuleConfig("{}");
        shop1.setBenefitConfig("[]");
        shop1.setWebConfig("{}");
        shop1.setPointBenefit(false);
        shop1.setUpdateTime(LocalDateTime.now());
        shop1.setCreateTime(LocalDateTime.now());

        SalesPromotionShopDTO shop2 = new SalesPromotionShopDTO();
        shop2.setBizCode("TEST_BIZ");
        shop2.setAgentCode("AGENT001");
        shop2.setPromotionToken("PROMO002");
        shop2.setPromotionType("COMPOSE");
        shop2.setPromotionName("组合优惠");
        shop2.setPromotionDesc("测试组合优惠");
        shop2.setEnabled(true);
        shop2.setStartTime(LocalDateTime.now().minusDays(1));
        shop2.setEndTime(LocalDateTime.now().plusDays(1));
        shop2.setRuleDesc("买二送一");
        shop2.setPromotionLevel("AGENT001");
        shop2.setPromotionScope("ALL");
        shop2.setRuleConfig("{}");
        shop2.setBenefitConfig("[]");
        shop2.setWebConfig("{}");
        shop2.setPointBenefit(false);
        shop2.setUpdateTime(LocalDateTime.now());
        shop2.setCreateTime(LocalDateTime.now());

        List<SalesPromotionShopDTO> mockResult = Arrays.asList(shop1, shop2);

        // 模拟服务调用
        when(salesPromotionService.listShopPromotions(any(PromotionShopListQry.class)))
                .thenReturn(mockResult);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/sales-promotion/shop-promotions")
                        .param("bizCode", "TEST_BIZ")
                        .param("agentCode", "AGENT001")
                        .param("secondAgentCode", "SECOND001")
                        .param("shopId", "SHOP001")
                        .param("productId", "PRODUCT001")
                        .param("skuId", "SKU001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].bizCode").value("TEST_BIZ"))
                .andExpect(jsonPath("$.data[0].agentCode").value("AGENT001"))
                .andExpect(jsonPath("$.data[0].promotionToken").value("PROMO001"))
                .andExpect(jsonPath("$.data[0].promotionType").value("SINGLE"))
                .andExpect(jsonPath("$.data[0].promotionName").value("单品优惠"))
                .andExpect(jsonPath("$.data[0].promotionDesc").value("测试单品优惠"))
                .andExpect(jsonPath("$.data[0].enabled").value(true))
                .andExpect(jsonPath("$.data[0].ruleDesc").value("满100元减10元"))
                .andExpect(jsonPath("$.data[0].promotionLevel").value("SHOP001"))
                .andExpect(jsonPath("$.data[0].promotionScope").value("ALL"))
                .andExpect(jsonPath("$.data[0].pointBenefit").value(false))
                .andExpect(jsonPath("$.data[1].promotionType").value("COMPOSE"))
                .andExpect(jsonPath("$.data[1].promotionName").value("组合优惠"))
                .andExpect(jsonPath("$.data[1].promotionLevel").value("AGENT001"));
    }

    @Test
    void testListShopPromotionsWithoutOptionalParams() throws Exception {
        // 构建模拟返回数据
        SalesPromotionShopDTO shop1 = new SalesPromotionShopDTO();
        shop1.setBizCode("TEST_BIZ");
        shop1.setPromotionToken("PROMO001");
        shop1.setPromotionType("SINGLE");
        shop1.setPromotionName("单品优惠");

        List<SalesPromotionShopDTO> mockResult = Arrays.asList(shop1);

        // 模拟服务调用
        when(salesPromotionService.listShopPromotions(any(PromotionShopListQry.class)))
                .thenReturn(mockResult);

        // 执行请求并验证结果 - 只传必填参数
        mockMvc.perform(get("/api/sales-promotion/shop-promotions")
                        .param("bizCode", "TEST_BIZ")
                        .param("shopId", "SHOP001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].bizCode").value("TEST_BIZ"))
                .andExpect(jsonPath("$.data[0].promotionType").value("SINGLE"));
    }

    @Test
    void testListShopPromotionsWithValidationError() throws Exception {
        // 测试参数验证 - 缺少必填参数 bizCode
        mockMvc.perform(get("/api/sales-promotion/shop-promotions")
                        .param("agentCode", "AGENT001")
                        .param("shopId", "SHOP001"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testListShopPromotionsWithValidationError2() throws Exception {
        // 测试参数验证 - 缺少必填参数 shopId
        mockMvc.perform(get("/api/sales-promotion/shop-promotions")
                        .param("bizCode", "TEST_BIZ")
                        .param("agentCode", "AGENT001"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testListShopPromotionsWithEmptyResult() throws Exception {
        // 模拟空结果
        when(salesPromotionService.listShopPromotions(any(PromotionShopListQry.class)))
                .thenReturn(Arrays.asList());

        // 执行请求并验证结果
        mockMvc.perform(get("/api/sales-promotion/shop-promotions")
                        .param("bizCode", "TEST_BIZ")
                        .param("shopId", "SHOP001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    void testListShopPromotionsWithScopeFilter() throws Exception {
        // 构建模拟返回数据
        SalesPromotionShopDTO shop1 = new SalesPromotionShopDTO();
        shop1.setBizCode("TEST_BIZ");
        shop1.setPromotionToken("PROMO001");
        shop1.setPromotionType("SINGLE");
        shop1.setPromotionName("单品优惠");

        List<SalesPromotionShopDTO> mockResult = Arrays.asList(shop1);

        // 模拟服务调用
        when(salesPromotionService.listShopPromotions(any(PromotionShopListQry.class)))
                .thenReturn(mockResult);

        // 执行请求并验证结果 - 包含商品和SKU参数进行范围过滤
        mockMvc.perform(get("/api/sales-promotion/shop-promotions")
                        .param("bizCode", "TEST_BIZ")
                        .param("shopId", "SHOP001")
                        .param("productId", "PRODUCT001")
                        .param("skuId", "SKU001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/json"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].promotionType").value("SINGLE"));
    }

}
